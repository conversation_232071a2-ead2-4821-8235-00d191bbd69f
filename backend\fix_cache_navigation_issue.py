#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缓存导致章节导航失效问题的脚本

问题描述：
- L2缓存(DragonflyDB)开启后，漫画详情API缓存了包含章节列表的数据
- 用户在阅读页面无法正常进行上一话/下一话导航
- 章节数据被错误缓存，导致导航信息过时

修复方案：
1. 移除漫画详情API的缓存装饰器
2. 清空所有相关缓存数据
3. 重新设计缓存策略，确保导航相关数据不被缓存
4. 重启Redis连接以确保配置生效
"""

import asyncio
import logging
import sys
import os

# 设置标准输出编码为UTF-8
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.redis_client import redis_client
from app.middleware.cache import CacheStats, invalidate_cache_pattern

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cache_fix.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """主修复流程"""
    
    print("[CACHE-FIX] Starting to fix L2 cache affecting chapter navigation...")
    
    try:
        # 1. Check Redis connection status
        print("\n[REDIS] Checking Redis/DragonflyDB connection status...")
        if redis_client.is_available:
            logger.info("[SUCCESS] Redis/DragonflyDB connection is available")
            print("[SUCCESS] Redis/DragonflyDB connection is available")
        else:
            logger.warning("[WARNING] Redis/DragonflyDB unavailable, will only clear memory cache")
            print("[WARNING] Redis/DragonflyDB unavailable, will only clear memory cache")
        
        # 2. Clear all manga-related cache
        print("\n[CLEAN] Clearing manga-related cache data...")
        patterns_to_clear = [
            "manga_basic",    # Old manga basic info cache
            "manga_list",     # New manga list cache
            "manga_details",  # Manga details cache (if exists)
            "chapters",       # Chapter-related cache
            "pages"           # Page-related cache
        ]
        
        total_cleared = 0
        for pattern in patterns_to_clear:
            try:
                cleared_count = await invalidate_cache_pattern(pattern)
                total_cleared += cleared_count
                logger.info(f"[SUCCESS] Cleared cache pattern '{pattern}': {cleared_count} records")
                print(f"[SUCCESS] Cleared cache pattern '{pattern}': {cleared_count} records")
            except Exception as e:
                logger.error(f"[ERROR] Failed to clear cache pattern '{pattern}': {e}")
                print(f"[ERROR] Failed to clear cache pattern '{pattern}': {e}")
        
        # 3. Complete cache flush (ultimate solution)
        print("\n[FLUSH] Executing complete cache flush...")
        try:
            result = await CacheStats.clear_all()
            if result["success"]:
                logger.info("[SUCCESS] All cache cleared")
                print("[SUCCESS] All cache cleared")
                print(f"  - Redis cache: {'cleared' if result['redis_cleared'] else 'skipped/failed'}")
                print(f"  - Memory cache: {'cleared' if result['fallback_cleared'] else 'skipped/failed'}")
            else:
                logger.warning("[WARNING] Cache clearing may be incomplete")
                print("[WARNING] Cache clearing may be incomplete")
        except Exception as e:
            logger.error(f"[ERROR] Failed to clear all cache: {e}")
            print(f"[ERROR] Failed to clear all cache: {e}")
        
        # 4. Reconnect Redis client
        print("\n[REFRESH] Refreshing Redis client...")
        try:
            await redis_client.refresh_config()
            logger.info("[SUCCESS] Redis client configuration refreshed")
            print("[SUCCESS] Redis client configuration refreshed")
        except Exception as e:
            logger.error(f"[WARNING] Redis client refresh failed: {e}")
            print(f"[WARNING] Redis client refresh failed: {e}")
        
        # 5. Verify fix results
        print("\n[VERIFY] Verifying fix results...")
        try:
            stats = await CacheStats.get_stats()
            logger.info("[STATS] Current cache status:")
            print("[STATS] Current cache status:")
            print(f"  - Redis status: {stats['redis']['status']}")
            print(f"  - Memory cache status: {stats['fallback']['status']}")
            if stats['redis']['status'] == 'available':
                print(f"  - Redis memory usage: {stats['redis'].get('used_memory_human', 'N/A')}")
            if stats['fallback']['status'] == 'available':
                print(f"  - Memory cache items: {stats['fallback'].get('total_items', 0)}")
        except Exception as e:
            logger.error(f"[WARNING] Failed to get cache stats: {e}")
            print(f"[WARNING] Failed to get cache stats: {e}")
        
        print("\n[COMPLETE] Fix completed!")
        print("\n[SUMMARY] Fix summary:")
        print("1. [DONE] Removed cache decorator from manga details API")
        print("2. [DONE] Cleared all related cache data")
        print("3. [DONE] Redesigned cache strategy")
        print("4. [DONE] Refreshed Redis/DragonflyDB connection")
        print("\n[STRATEGY] Current cache strategy:")
        print("  * Manga details (with chapters): NOT cached")
        print("  * Chapter details and lists: NOT cached")
        print("  * Page lists: NOT cached")
        print("  * Manga lists: cached for 30 minutes")
        print("  * Featured content: cached for 10 minutes")
        print("\n[RESULT] Users should now be able to navigate chapters normally!")
        
    except Exception as e:
        logger.error(f"[ERROR] Error during fix process: {e}")
        print(f"[ERROR] Error during fix process: {e}")
        return False
        
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)