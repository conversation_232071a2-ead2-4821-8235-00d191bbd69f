'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { AlertCircle, CheckCircle, Database, Settings, Loader2, Trash2, BarChart3 } from 'lucide-react';
import { apiClient } from '@/lib/api';

interface RedisConfig {
  redis_host: string;
  redis_port: number;
  redis_db: number;
  redis_password: string;
  redis_max_memory: number;
  redis_expire_time: number;
  redis_max_connections: number;
  redis_enabled: boolean;
}

interface RedisStatus {
  connected: boolean;
  enabled: boolean;
  error?: string;
  memory_info?: {
    used_memory_human?: string;
    maxmemory_human?: string;
  };
}

interface CacheStats {
  redis?: {
    status: string;
    used_memory_human?: string;
    keyspace_hits?: number;
    keyspace_misses?: number;
    hit_ratio?: number;
  };
  fallback?: {
    status: string;
    total_items?: number;
    memory_usage_estimate?: number;
  };
}

const RedisConfiguration = () => {
  const [config, setConfig] = useState<RedisConfig>({
    redis_host: 'localhost',
    redis_port: 6379,
    redis_db: 0,
    redis_password: '',
    redis_max_memory: 0,
    redis_expire_time: 3600,
    redis_max_connections: 50,
    redis_enabled: false
  });

  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [status, setStatus] = useState<RedisStatus | null>(null);
  const [message, setMessage] = useState<string>('');
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [clearingCache, setClearingCache] = useState(false);

  useEffect(() => {
    loadConfig();
    loadStatus();
    loadCacheStats();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getRedisConfig();
      setConfig(data);
    } catch (error) {
      console.error('加载Redis配置失败:', error);
      setMessage('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStatus = async () => {
    try {
      const data = await apiClient.getRedisStatus();
      setStatus(data);
    } catch (error) {
      console.error('获取Redis状态失败:', error);
    }
  };

  const saveConfig = async () => {
    try {
      setLoading(true);
      setMessage('');
      
      const result = await apiClient.updateRedisConfig(config);
      setMessage('Redis配置保存成功');
      // 更新状态
      await loadStatus();
    } catch (error) {
      console.error('保存Redis配置失败:', error);
      setMessage(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    if (!config.redis_enabled) {
      setMessage('请先启用Redis');
      return;
    }

    try {
      setTesting(true);
      setMessage('');
      
      const testData = {
        host: config.redis_host,
        port: config.redis_port,
        db: config.redis_db,
        password: config.redis_password || undefined
      };

      const result = await apiClient.testRedisConnection(testData);
      if (result.success) {
        setMessage(`连接测试成功 - Redis版本: ${result.server_info?.redis_version || '未知'}`);
      } else {
        setMessage(`连接测试失败: ${result.message}`);
      }
    } catch (error) {
      console.error('测试Redis连接失败:', error);
      setMessage(`测试失败: ${error instanceof Error ? error.message : '网络错误'}`);
    } finally {
      setTesting(false);
    }
  };

  const loadCacheStats = async () => {
    try {
      const stats = await apiClient.getCacheStats();
      setCacheStats(stats);
    } catch (error) {
      console.error('获取缓存统计失败:', error);
    }
  };

  const clearCache = async () => {
    try {
      setClearingCache(true);
      setMessage('');
      
      const result = await apiClient.clearAllCache();
      if (result.success) {
        setMessage('缓存清理成功');
        await loadCacheStats();
        await loadStatus();
      } else {
        setMessage(`缓存清理失败: ${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('清理缓存失败:', error);
      setMessage(`清理失败: ${error instanceof Error ? error.message : '网络错误'}`);
    } finally {
      setClearingCache(false);
    }
  };

  const handleConfigChange = (key: keyof RedisConfig, value: string | number | boolean) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Redis / DragonflyDB 配置管理
          </CardTitle>
          <CardDescription>
            配置Redis或DragonflyDB服务器连接参数。DragonflyDB完全兼容Redis协议，无需特殊配置。
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 启用开关 */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="redis-enabled">启用Redis/DragonflyDB缓存</Label>
              <p className="text-sm text-muted-foreground">
                启用后系统将使用Redis或DragonflyDB进行数据缓存（完全兼容）
              </p>
            </div>
            <Switch
              id="redis-enabled"
              checked={config.redis_enabled}
              onCheckedChange={(checked) => handleConfigChange('redis_enabled', checked)}
            />
          </div>

          {/* 连接状态显示 */}
          {config.redis_enabled && (
            <div className="p-3 border rounded-lg bg-muted/30">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">连接状态</span>
                <div className="flex items-center gap-2">
                  {status?.connected ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                  <span className="text-sm">
                    {status?.connected ? '已连接' : (status?.error ? '连接失败' : '未连接')}
                  </span>
                </div>
              </div>
              {status?.memory_info && (
                <div className="text-xs text-muted-foreground">
                  内存使用: {status.memory_info.used_memory_human}
                  {status.memory_info.maxmemory_human && ` / ${status.memory_info.maxmemory_human}`}
                </div>
              )}
            </div>
          )}

          {/* 缓存统计和管理 */}
          {config.redis_enabled && status?.connected && (
            <Card className="bg-blue-50/30 border-blue-200">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-base">
                  <BarChart3 className="w-4 h-4" />
                  缓存统计与管理
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {cacheStats && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Redis缓存统计 */}
                    {cacheStats.redis && (
                      <div className="p-3 bg-white border rounded-lg">
                        <h4 className="font-medium text-sm mb-2">Redis缓存</h4>
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <div>状态: <span className="font-medium">{cacheStats.redis.status}</span></div>
                          {cacheStats.redis.used_memory_human && (
                            <div>内存使用: <span className="font-medium">{cacheStats.redis.used_memory_human}</span></div>
                          )}
                          {cacheStats.redis.hit_ratio !== undefined && (
                            <div>命中率: <span className="font-medium">{cacheStats.redis.hit_ratio}%</span></div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* 内存缓存统计 */}
                    {cacheStats.fallback && (
                      <div className="p-3 bg-white border rounded-lg">
                        <h4 className="font-medium text-sm mb-2">内存缓存</h4>
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <div>状态: <span className="font-medium">{cacheStats.fallback.status}</span></div>
                          {cacheStats.fallback.total_items !== undefined && (
                            <div>缓存项目: <span className="font-medium">{cacheStats.fallback.total_items}</span></div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {/* 缓存管理按钮 */}
                <div className="flex gap-2 pt-2">
                  <Button
                    onClick={clearCache}
                    disabled={clearingCache}
                    variant="destructive"
                    size="sm"
                    className="flex-1"
                  >
                    {clearingCache ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        清理中...
                      </>
                    ) : (
                      <>
                        <Trash2 className="w-4 h-4 mr-2" />
                        清空缓存
                      </>
                    )}
                  </Button>
                  
                  <Button
                    onClick={loadCacheStats}
                    variant="outline"
                    size="sm"
                  >
                    <BarChart3 className="w-4 h-4 mr-2" />
                    刷新统计
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 基本配置 */}
          {config.redis_enabled && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="redis-host">服务器地址</Label>
                  <Input
                    id="redis-host"
                    value={config.redis_host}
                    onChange={(e) => handleConfigChange('redis_host', e.target.value)}
                    placeholder="localhost"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="redis-port">端口</Label>
                  <Input
                    id="redis-port"
                    type="number"
                    min="1"
                    max="65535"
                    value={config.redis_port}
                    onChange={(e) => handleConfigChange('redis_port', parseInt(e.target.value) || 6379)}
                    placeholder="6379"
                  />
                  <p className="text-xs text-muted-foreground">
                    常用端口：Redis = 6379，DragonflyDB = 16379
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="redis-db">数据库编号</Label>
                  <Input
                    id="redis-db"
                    type="number"
                    min="0"
                    max="15"
                    value={config.redis_db}
                    onChange={(e) => handleConfigChange('redis_db', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="redis-password">密码（可选）</Label>
                  <Input
                    id="redis-password"
                    type="password"
                    value={config.redis_password}
                    onChange={(e) => handleConfigChange('redis_password', e.target.value)}
                    placeholder="留空表示无密码"
                  />
                </div>
              </div>

              {/* 高级设置 */}
              <details className="space-y-4">
                <summary className="cursor-pointer text-sm font-medium flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  高级设置
                </summary>
                <div className="pl-6 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="redis-max-memory">最大内存限制 (MB，0=无限制)</Label>
                    <Input
                      id="redis-max-memory"
                      type="number"
                      min="0"
                      value={config.redis_max_memory}
                      onChange={(e) => handleConfigChange('redis_max_memory', parseInt(e.target.value) || 0)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="redis-expire-time">默认过期时间 (秒)</Label>
                    <Input
                      id="redis-expire-time"
                      type="number"
                      min="1"
                      value={config.redis_expire_time}
                      onChange={(e) => handleConfigChange('redis_expire_time', parseInt(e.target.value) || 3600)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="redis-max-connections">最大连接数</Label>
                    <Input
                      id="redis-max-connections"
                      type="number"
                      min="1"
                      value={config.redis_max_connections}
                      onChange={(e) => handleConfigChange('redis_max_connections', parseInt(e.target.value) || 50)}
                    />
                  </div>
                </div>
              </details>
            </div>
          )}

          {/* 状态消息 */}
          {message && (
            <div className={`p-3 rounded-lg text-sm ${
              message.includes('成功')
                ? 'bg-green-50 text-green-800 border border-green-200'
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-2 pt-4 border-t">
            <Button
              onClick={saveConfig}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                '保存配置'
              )}
            </Button>
            
            {config.redis_enabled && (
              <Button
                onClick={testConnection}
                disabled={testing}
                variant="outline"
              >
                {testing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    测试中...
                  </>
                ) : (
                  '测试连接'
                )}
              </Button>
            )}
            
            <Button
              onClick={() => {
                loadConfig();
                loadStatus();
              }}
              disabled={loading}
              variant="outline"
            >
              刷新
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RedisConfiguration;