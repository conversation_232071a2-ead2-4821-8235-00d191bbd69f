#!/usr/bin/env python3
"""
修复 comments 表中 anime_id 字段的 nullable 约束问题
这个脚本直接修改数据库，使 anime_id 字段允许为 NULL
"""

import sys
sys.path.append('.')

from app.core.database import engine
from sqlalchemy import text

def fix_anime_id_nullable():
    """修复 anime_id 字段约束"""
    try:
        with engine.connect() as conn:
            # 检查当前表结构
            result = conn.execute(text("DESCRIBE comments"))
            print("当前 comments 表结构:")
            for row in result:
                if row[0] == 'anime_id':
                    print(f"anime_id: {row[1]} NULL={row[2]} Key={row[3]} Default={row[4]}")
            
            # 修改 anime_id 字段允许 NULL
            print("\n正在修改 anime_id 字段为可 NULL...")
            conn.execute(text("""
                ALTER TABLE comments 
                MODIFY COLUMN anime_id INT NULL
            """))
            conn.commit()
            
            # 再次检查表结构
            result = conn.execute(text("DESCRIBE comments"))
            print("\n修改后的 comments 表结构:")
            for row in result:
                if row[0] == 'anime_id':
                    print(f"anime_id: {row[1]} NULL={row[2]} Key={row[3]} Default={row[4]}")
                    
            print("✅ anime_id 字段修改成功!")
            
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("开始修复 comments 表 anime_id 字段约束...")
    success = fix_anime_id_nullable()
    if success:
        print("修复完成! 现在可以创建漫画评论了。")
    else:
        print("修复失败，请检查错误信息。")