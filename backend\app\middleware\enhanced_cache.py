"""
增强的三层缓存架构实现
L1: 应用内存缓存 (热点数据)
L2: Redis/DragonflyDB缓存 (常用数据)  
L3: 数据库 (完整数据)

核心原则：
1. 缓存只是性能优化，绝不影响功能
2. L2缓存开启/关闭对用户完全透明
3. 数据一致性通过版本号和TTL机制保证
"""
import json
import hashlib
import asyncio
from typing import Optional, Any, Dict, List, Callable
from functools import wraps
import time
import logging
from datetime import datetime, timedelta
from app.core.redis_client import redis_client
from app.core.config import settings

logger = logging.getLogger(__name__)

# ==================== 缓存策略配置 ====================
class CacheStrategy:
    """缓存策略定义"""
    
    # 热点数据缓存策略
    HOT_DATA = {
        # 列表类数据（可缓存，不影响功能）
        "anime_list": {"ttl": 300, "priority": "L2", "invalidate_on": ["anime_update", "anime_create", "anime_delete"]},
        "manga_list": {"ttl": 300, "priority": "L2", "invalidate_on": ["manga_update", "manga_create", "manga_delete"]},
        "popular_content": {"ttl": 600, "priority": "L2", "invalidate_on": ["view_count_update"]},
        "featured_content": {"ttl": 600, "priority": "L2", "invalidate_on": ["featured_update"]},
        "categories": {"ttl": 3600, "priority": "L2", "invalidate_on": ["category_update"]},
        "tags": {"ttl": 3600, "priority": "L2", "invalidate_on": ["tag_update"]},
        
        # 统计数据（可缓存，定期更新）
        "stats": {"ttl": 300, "priority": "L2", "invalidate_on": ["stats_refresh"]},
        "rankings": {"ttl": 600, "priority": "L2", "invalidate_on": ["ranking_update"]},
    }
    
    # 会话数据缓存策略
    SESSION_DATA = {
        # 用户会话相关（短时缓存）
        "user_session": {"ttl": 1800, "priority": "L1", "invalidate_on": ["user_logout"]},
        "user_preferences": {"ttl": 3600, "priority": "L2", "invalidate_on": ["preference_update"]},
        "search_history": {"ttl": 900, "priority": "L1", "invalidate_on": ["search_clear"]},
    }
    
    # 频繁访问的静态数据
    STATIC_DATA = {
        # 配置类数据（长时缓存）
        "system_config": {"ttl": 7200, "priority": "L2", "invalidate_on": ["config_update"]},
        "player_config": {"ttl": 3600, "priority": "L2", "invalidate_on": ["player_config_update"]},
        "announcements": {"ttl": 1800, "priority": "L2", "invalidate_on": ["announcement_update"]},
    }
    
    # ⚠️ 绝对不缓存的数据类型 - 保证功能正确性
    NO_CACHE = [
        "manga_detail",          # 漫画详情（包含章节列表）
        "chapter_list",          # 章节列表
        "chapter_detail",        # 章节详情
        "chapter_pages",         # 章节页面
        "reading_progress",      # 阅读进度
        "user_auth",            # 用户认证
        "favorites_status",      # 收藏状态
        "comments",             # 评论数据
        "real_time_stats",      # 实时统计
        "payment_data",         # 支付相关
        "sensitive_data",       # 敏感数据
    ]

# ==================== 数据版本控制 ====================
class CacheVersionManager:
    """缓存版本管理器 - 确保数据一致性"""
    
    VERSION_KEY_PREFIX = "cache_version:"
    
    @classmethod
    async def get_version(cls, data_type: str) -> int:
        """获取数据版本号"""
        if not redis_client.is_available:
            return int(time.time())
        
        try:
            version_key = f"{cls.VERSION_KEY_PREFIX}{data_type}"
            version = await redis_client.get(version_key)
            return int(version) if version else 0
        except Exception as e:
            logger.warning(f"Failed to get version for {data_type}: {e}")
            return int(time.time())
    
    @classmethod
    async def increment_version(cls, data_type: str) -> int:
        """增加数据版本号（数据更新时调用）"""
        if not redis_client.is_available:
            return int(time.time())
        
        try:
            version_key = f"{cls.VERSION_KEY_PREFIX}{data_type}"
            # 使用Redis的原子操作
            new_version = await redis_client.redis_client.incr(version_key)
            # 设置版本号过期时间（7天）
            await redis_client.redis_client.expire(version_key, 604800)
            return new_version
        except Exception as e:
            logger.warning(f"Failed to increment version for {data_type}: {e}")
            return int(time.time())
    
    @classmethod
    async def invalidate_related(cls, event: str):
        """根据事件使相关缓存失效"""
        # 找出所有受此事件影响的缓存策略
        affected_strategies = []
        
        for strategy_dict in [CacheStrategy.HOT_DATA, CacheStrategy.SESSION_DATA, CacheStrategy.STATIC_DATA]:
            for key, config in strategy_dict.items():
                if event in config.get("invalidate_on", []):
                    affected_strategies.append(key)
        
        # 更新所有受影响的数据版本
        for strategy in affected_strategies:
            await cls.increment_version(strategy)
            logger.info(f"Invalidated cache for {strategy} due to event: {event}")

# ==================== 智能缓存装饰器 ====================
def smart_cache(cache_type: str, custom_ttl: Optional[int] = None):
    """
    智能缓存装饰器 - 根据策略自动选择缓存层级
    
    Args:
        cache_type: 缓存类型（对应策略中的key）
        custom_ttl: 自定义TTL（可选）
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 检查是否是不可缓存的数据类型
            if cache_type in CacheStrategy.NO_CACHE:
                return await func(*args, **kwargs)
            
            # 检查全局缓存开关
            if not getattr(settings, 'ENABLE_CACHE', True):
                return await func(*args, **kwargs)
            
            # 获取缓存策略
            strategy = None
            for strategy_dict in [CacheStrategy.HOT_DATA, CacheStrategy.SESSION_DATA, CacheStrategy.STATIC_DATA]:
                if cache_type in strategy_dict:
                    strategy = strategy_dict[cache_type]
                    break
            
            if not strategy:
                # 未定义的缓存类型，直接执行
                logger.warning(f"Unknown cache type: {cache_type}")
                return await func(*args, **kwargs)
            
            # 生成缓存键（包含版本号）
            try:
                version = await CacheVersionManager.get_version(cache_type)
                cache_key = generate_cache_key(func.__name__, version, *args, **kwargs)
                cache_key = f"{cache_type}:{cache_key}"
            except Exception as e:
                logger.warning(f"Failed to generate cache key: {e}")
                return await func(*args, **kwargs)
            
            ttl = custom_ttl or strategy.get("ttl", 300)
            priority = strategy.get("priority", "L2")
            
            # 根据优先级尝试获取缓存
            cached_result = None
            
            # L1缓存（内存）
            if priority in ["L1", "L2"]:
                cached_result = L1Cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"L1 cache hit: {cache_key}")
                    return cached_result
            
            # L2缓存（Redis/DragonflyDB）
            if priority == "L2" and redis_client.is_available:
                try:
                    cached_result = await redis_client.get(cache_key)
                    if cached_result is not None:
                        logger.debug(f"L2 cache hit: {cache_key}")
                        # 回填L1缓存
                        L1Cache.set(cache_key, cached_result, ttl=min(ttl, 60))
                        return cached_result
                except Exception as e:
                    logger.warning(f"L2 cache read failed: {e}")
            
            # 缓存未命中，执行原函数
            result = await func(*args, **kwargs)
            
            # 异步写入缓存（不阻塞返回）
            asyncio.create_task(
                write_to_cache(cache_key, result, ttl, priority)
            )
            
            return result
        
        return wrapper
    return decorator

async def write_to_cache(cache_key: str, data: Any, ttl: int, priority: str):
    """异步写入缓存（不影响主流程）"""
    try:
        # 写入L1缓存
        if priority in ["L1", "L2"]:
            L1Cache.set(cache_key, data, ttl=min(ttl, 60))
        
        # 写入L2缓存
        if priority == "L2" and redis_client.is_available:
            try:
                await redis_client.set(cache_key, data, ttl)
                logger.debug(f"L2 cache written: {cache_key}")
            except Exception as e:
                logger.warning(f"L2 cache write failed: {e}")
    except Exception as e:
        logger.error(f"Cache write failed: {e}")

# ==================== L1内存缓存实现 ====================
class L1CacheManager:
    """L1内存缓存管理器 - LRU策略"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 60):
        self._cache: Dict[str, Any] = {}
        self._timestamps: Dict[str, float] = {}
        self._access_times: Dict[str, float] = {}
        self._ttls: Dict[str, int] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key not in self._cache:
            return None
        
        # 检查是否过期
        if time.time() - self._timestamps[key] > self._ttls.get(key, self.default_ttl):
            self._evict(key)
            return None
        
        # 更新访问时间
        self._access_times[key] = time.time()
        return self._cache[key]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存项"""
        # 如果缓存已满，执行LRU淘汰
        if len(self._cache) >= self.max_size and key not in self._cache:
            self._evict_lru()
        
        self._cache[key] = value
        self._timestamps[key] = time.time()
        self._access_times[key] = time.time()
        self._ttls[key] = ttl or self.default_ttl
    
    def _evict(self, key: str):
        """淘汰指定缓存项"""
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]
            del self._access_times[key]
            if key in self._ttls:
                del self._ttls[key]
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        if not self._access_times:
            return
        
        # 找出最久未访问的项
        lru_key = min(self._access_times, key=self._access_times.get)
        self._evict(lru_key)
        logger.debug(f"LRU evicted: {lru_key}")
    
    def clear(self):
        """清空所有缓存"""
        self._cache.clear()
        self._timestamps.clear()
        self._access_times.clear()
        self._ttls.clear()
    
    def cleanup_expired(self):
        """清理过期项"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._timestamps.items()
            if current_time - timestamp > self._ttls.get(key, self.default_ttl)
        ]
        
        for key in expired_keys:
            self._evict(key)
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired L1 cache entries")

# 创建L1缓存实例
L1Cache = L1CacheManager(max_size=1000, default_ttl=60)

# ==================== 缓存键生成器 ====================
def generate_cache_key(func_name: str, version: int, *args, **kwargs) -> str:
    """生成缓存键（包含版本号确保一致性）"""
    try:
        # 过滤掉不应该作为缓存键一部分的参数
        filtered_kwargs = {
            k: v for k, v in kwargs.items() 
            if k not in ['db', 'session', 'request', 'response']
        }
        
        key_data = {
            'func': func_name,
            'version': version,
            'args': [str(arg) for arg in args if not hasattr(arg, '__dict__')],
            'kwargs': filtered_kwargs
        }
        
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    except Exception as e:
        logger.warning(f"Cache key generation failed: {e}")
        # 降级方案：使用简单的键
        return f"{func_name}_{version}_{int(time.time() * 1000000) % 1000000}"

# ==================== 缓存预热 ====================
class CacheWarmer:
    """缓存预热器 - 提前加载热点数据"""
    
    @classmethod
    async def warm_hot_data(cls):
        """预热热点数据"""
        logger.info("Starting cache warming...")
        
        try:
            # 这里可以添加预热逻辑
            # 例如：预加载热门动漫/漫画列表、分类信息等
            pass
        except Exception as e:
            logger.error(f"Cache warming failed: {e}")
    
    @classmethod
    async def schedule_warming(cls):
        """定期预热（每小时执行一次）"""
        while True:
            await asyncio.sleep(3600)  # 1小时
            await cls.warm_hot_data()

# ==================== 缓存监控 ====================
class CacheMonitor:
    """缓存监控器 - 监控缓存性能"""
    
    @classmethod
    async def get_metrics(cls) -> Dict:
        """获取缓存指标"""
        metrics = {
            "l1": {
                "size": len(L1Cache._cache),
                "max_size": L1Cache.max_size,
                "utilization": f"{len(L1Cache._cache) / L1Cache.max_size * 100:.1f}%"
            },
            "l2": {
                "available": redis_client.is_available,
                "status": "connected" if redis_client.is_available else "disconnected"
            },
            "timestamp": datetime.now().isoformat()
        }
        
        # 获取Redis详细指标
        if redis_client.is_available:
            try:
                info = await redis_client.redis_client.info()
                metrics["l2"].update({
                    "memory_used": info.get("used_memory_human", "N/A"),
                    "hit_rate": calculate_hit_rate(
                        info.get("keyspace_hits", 0),
                        info.get("keyspace_misses", 0)
                    ),
                    "total_keys": await redis_client.redis_client.dbsize()
                })
            except Exception as e:
                logger.error(f"Failed to get Redis metrics: {e}")
        
        return metrics

def calculate_hit_rate(hits: int, misses: int) -> str:
    """计算缓存命中率"""
    total = hits + misses
    if total == 0:
        return "0.0%"
    return f"{hits / total * 100:.1f}%"

# ==================== 自动清理任务 ====================
async def auto_cleanup_task():
    """定期清理过期缓存"""
    while True:
        await asyncio.sleep(300)  # 5分钟
        try:
            L1Cache.cleanup_expired()
            logger.debug("Auto cleanup completed")
        except Exception as e:
            logger.error(f"Auto cleanup failed: {e}")

# 启动自动清理任务（在应用启动时调用）
def start_cache_maintenance():
    """启动缓存维护任务"""
    asyncio.create_task(auto_cleanup_task())
    asyncio.create_task(CacheWarmer.schedule_warming())
    logger.info("Cache maintenance tasks started")