"""
缓存数据一致性保证机制
确保缓存数据与数据库保持一致
"""
import asyncio
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
from functools import wraps
from app.core.redis_client import redis_client
from app.middleware.enhanced_cache import CacheVersionManager, L1Cache

logger = logging.getLogger(__name__)

class CacheConsistencyManager:
    """缓存一致性管理器"""
    
    # 事件与缓存关系映射
    EVENT_CACHE_MAP = {
        # 动漫相关事件
        "anime.created": ["anime_list", "stats", "popular_content"],
        "anime.updated": ["anime_list", "anime_detail", "popular_content"],
        "anime.deleted": ["anime_list", "stats", "popular_content"],
        "anime.view_incremented": ["popular_content", "rankings"],
        
        # 漫画相关事件  
        "manga.created": ["manga_list", "stats", "popular_content"],
        "manga.updated": ["manga_list", "popular_content"],
        "manga.deleted": ["manga_list", "stats", "popular_content"],
        "manga.view_incremented": ["popular_content", "rankings"],
        
        # 章节相关事件（不缓存，但要清理可能的错误缓存）
        "chapter.created": ["chapter_list"],
        "chapter.updated": ["chapter_list", "chapter_detail"],
        "chapter.deleted": ["chapter_list"],
        
        # 用户相关事件
        "user.login": ["user_session"],
        "user.logout": ["user_session", "user_preferences"],
        "user.updated": ["user_session", "user_preferences"],
        
        # 收藏相关事件
        "favorite.added": ["popular_content", "stats"],
        "favorite.removed": ["popular_content", "stats"],
        
        # 系统配置事件
        "config.updated": ["system_config"],
        "announcement.updated": ["announcements"],
        "featured.updated": ["featured_content"],
    }
    
    @classmethod
    async def emit_event(cls, event_name: str, data: Optional[Dict[str, Any]] = None):
        """
        发出事件，触发相关缓存失效
        
        Args:
            event_name: 事件名称（如 "anime.updated"）
            data: 事件相关数据
        """
        logger.info(f"Cache consistency event: {event_name}")
        
        # 获取受影响的缓存类型
        affected_caches = cls.EVENT_CACHE_MAP.get(event_name, [])
        
        if not affected_caches:
            logger.debug(f"No cache affected by event: {event_name}")
            return
        
        # 失效相关缓存
        for cache_type in affected_caches:
            await cls.invalidate_cache(cache_type, data)
        
        # 更新版本号
        await CacheVersionManager.invalidate_related(event_name.replace(".", "_"))
    
    @classmethod
    async def invalidate_cache(cls, cache_type: str, data: Optional[Dict[str, Any]] = None):
        """
        失效指定类型的缓存
        
        Args:
            cache_type: 缓存类型
            data: 相关数据（用于精确失效）
        """
        try:
            # 清理L1缓存
            cls._invalidate_l1_cache(cache_type, data)
            
            # 清理L2缓存
            if redis_client.is_available:
                await cls._invalidate_l2_cache(cache_type, data)
            
            logger.info(f"Invalidated cache for type: {cache_type}")
        except Exception as e:
            logger.error(f"Failed to invalidate cache for {cache_type}: {e}")
    
    @classmethod
    def _invalidate_l1_cache(cls, cache_type: str, data: Optional[Dict[str, Any]] = None):
        """清理L1内存缓存"""
        # 获取所有相关的缓存键
        keys_to_delete = []
        for key in list(L1Cache._cache.keys()):
            if key.startswith(f"{cache_type}:"):
                keys_to_delete.append(key)
        
        # 删除缓存项
        for key in keys_to_delete:
            L1Cache._evict(key)
        
        if keys_to_delete:
            logger.debug(f"Invalidated {len(keys_to_delete)} L1 cache entries for {cache_type}")
    
    @classmethod
    async def _invalidate_l2_cache(cls, cache_type: str, data: Optional[Dict[str, Any]] = None):
        """清理L2 Redis缓存"""
        try:
            pattern = f"{cache_type}:*"
            deleted_count = await redis_client.clear_pattern(pattern)
            if deleted_count > 0:
                logger.debug(f"Invalidated {deleted_count} L2 cache entries for {cache_type}")
        except Exception as e:
            logger.error(f"Failed to invalidate L2 cache for {cache_type}: {e}")
    
    @classmethod
    async def ensure_consistency(cls, model_type: str, model_id: int, operation: str):
        """
        确保数据一致性的便捷方法
        
        Args:
            model_type: 模型类型（anime, manga, user等）
            model_id: 模型ID
            operation: 操作类型（created, updated, deleted）
        """
        event_name = f"{model_type}.{operation}"
        await cls.emit_event(event_name, {"id": model_id})

class CacheTransaction:
    """
    缓存事务 - 确保缓存操作的原子性
    """
    
    def __init__(self):
        self.operations = []
        self.rollback_operations = []
    
    def add_operation(self, operation: callable, rollback: callable):
        """添加操作和对应的回滚操作"""
        self.operations.append(operation)
        self.rollback_operations.append(rollback)
    
    async def commit(self):
        """提交事务"""
        executed = []
        try:
            for operation in self.operations:
                await operation()
                executed.append(operation)
        except Exception as e:
            logger.error(f"Cache transaction failed: {e}")
            # 回滚已执行的操作
            await self._rollback(executed)
            raise
    
    async def _rollback(self, executed_operations: List[callable]):
        """回滚操作"""
        for i, operation in enumerate(executed_operations):
            try:
                rollback = self.rollback_operations[i]
                await rollback()
            except Exception as e:
                logger.error(f"Rollback failed for operation {i}: {e}")

class CacheHealthChecker:
    """缓存健康检查器"""
    
    @classmethod
    async def check_health(cls) -> Dict[str, Any]:
        """检查缓存系统健康状态"""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }
        
        # 检查L1缓存
        try:
            l1_status = {
                "status": "healthy",
                "size": len(L1Cache._cache),
                "max_size": L1Cache.max_size,
                "utilization": f"{len(L1Cache._cache) / L1Cache.max_size * 100:.1f}%"
            }
            health_status["components"]["l1_cache"] = l1_status
        except Exception as e:
            health_status["components"]["l1_cache"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        # 检查L2缓存
        try:
            if redis_client.is_available:
                # 测试Redis连接
                await redis_client.redis_client.ping()
                info = await redis_client.redis_client.info()
                
                l2_status = {
                    "status": "healthy",
                    "connected": True,
                    "memory_used": info.get("used_memory_human", "N/A"),
                    "uptime_days": info.get("uptime_in_days", 0)
                }
            else:
                l2_status = {
                    "status": "unavailable",
                    "connected": False,
                    "message": "Redis/DragonflyDB not configured or not available"
                }
                health_status["status"] = "degraded"
            
            health_status["components"]["l2_cache"] = l2_status
        except Exception as e:
            health_status["components"]["l2_cache"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        # 检查数据一致性
        try:
            consistency_status = await cls._check_consistency()
            health_status["components"]["consistency"] = consistency_status
        except Exception as e:
            health_status["components"]["consistency"] = {
                "status": "unknown",
                "error": str(e)
            }
        
        return health_status
    
    @classmethod
    async def _check_consistency(cls) -> Dict[str, Any]:
        """检查数据一致性"""
        # 这里可以添加具体的一致性检查逻辑
        # 例如：抽样检查缓存数据与数据库是否一致
        return {
            "status": "healthy",
            "last_check": datetime.now().isoformat(),
            "message": "Data consistency check passed"
        }

class CacheSafetyWrapper:
    """
    缓存安全包装器 - 确保缓存操作不影响主功能
    """
    
    @staticmethod
    def safe_cache_operation(func):
        """装饰器：确保缓存操作失败不影响主功能"""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Cache operation failed in {func.__name__}: {e}")
                # 缓存操作失败，返回None或默认值
                return None
        return wrapper
    
    @staticmethod
    async def safe_get(cache_key: str, default=None):
        """安全获取缓存"""
        try:
            # 尝试L1缓存
            result = L1Cache.get(cache_key)
            if result is not None:
                return result
            
            # 尝试L2缓存
            if redis_client.is_available:
                result = await redis_client.get(cache_key)
                if result is not None:
                    return result
        except Exception as e:
            logger.warning(f"Safe get failed for {cache_key}: {e}")
        
        return default
    
    @staticmethod
    async def safe_set(cache_key: str, value: Any, ttl: int = 300):
        """安全设置缓存"""
        try:
            # 设置L1缓存
            L1Cache.set(cache_key, value, ttl=min(ttl, 60))
            
            # 设置L2缓存
            if redis_client.is_available:
                await redis_client.set(cache_key, value, ttl)
            
            return True
        except Exception as e:
            logger.warning(f"Safe set failed for {cache_key}: {e}")
            return False

# 缓存一致性中间件
async def cache_consistency_middleware(request, call_next):
    """
    缓存一致性中间件
    监控数据修改操作并自动失效相关缓存
    """
    response = await call_next(request)
    
    # 检查是否是数据修改操作
    if request.method in ["POST", "PUT", "DELETE", "PATCH"]:
        # 解析路径获取操作类型
        path_parts = request.url.path.strip("/").split("/")
        
        if len(path_parts) >= 2:
            resource_type = path_parts[1]  # 如 "animes", "manga", "users"
            operation = "updated"  # 默认操作
            
            if request.method == "POST":
                operation = "created"
            elif request.method == "DELETE":
                operation = "deleted"
            
            # 发出事件
            event_name = f"{resource_type.rstrip('s')}.{operation}"
            await CacheConsistencyManager.emit_event(event_name)
    
    return response

# 导出主要组件
__all__ = [
    "CacheConsistencyManager",
    "CacheTransaction",
    "CacheHealthChecker",
    "CacheSafetyWrapper",
    "cache_consistency_middleware"
]